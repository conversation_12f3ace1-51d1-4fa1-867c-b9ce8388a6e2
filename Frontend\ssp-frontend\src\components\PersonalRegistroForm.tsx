import React, { useState, useEffect } from 'react';
import {
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  Typography,
  Box,
  Autocomplete,
  Alert
} from '@mui/material';
import type { PersonaCreate, Cohorte } from '../types/index';
import { cohortesApi } from '@/services/api';

interface PersonalRegistroFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (persona: PersonaCreate) => void;
  loading?: boolean;
}

const PersonalRegistroForm = ({ open, onClose, onSubmit, loading = false }: PersonalRegistroFormProps) => {
  const [formData, setFormData] = useState<PersonaCreate>({
    tipo_persona: 'administrativo',
    correo_institucional: '',
    rol: 'personal',
    password: '',
    confirmPassword: '',
    sexo: 'masculino',
    genero: 'masculino',
    edad: 25,
    estado_civil: 'soltero',
    religion: '',
    trabaja: true,
    lugar_trabajo: '',
    lugar_origen: '',
    colonia_residencia_actual: '',
    celular: '',
    discapacidad: '',
    observaciones: '',
    matricula: '',
    semestre: null,
    numero_hijos: 0,
    grupo_etnico: '',
    cohorte_id: null,
    programas_ids: [],
    grupos_ids: [],
  });

  const [cohortes, setCohortes] = useState<Cohorte[]>([]);
  const [selectedCohorte, setSelectedCohorte] = useState<Cohorte | null>(null);

  // Áreas de trabajo para personal académico
  const areasPersonal = [
    'Servicios Estudiantiles',
    'Psicopedagogía',
    'Orientación Educativa',
    'Coordinación Académica',
    'Administración Escolar',
    'Recursos Humanos',
    'Sistemas y Tecnología',
    'Biblioteca',
    'Laboratorios',
    'Otra'
  ];

  // Cargar cohortes
  useEffect(() => {
    const loadCohortes = async () => {
      try {
        const cohortesData = await cohortesApi.getAll();
        setCohortes(cohortesData);
      } catch (error) {
        console.error('Error cargando cohortes:', error);
      }
    };

    if (open) {
      loadCohortes();
    }
  }, [open]);

  // Reset form when dialog closes
  useEffect(() => {
    if (!open) {
      setFormData({
        tipo_persona: 'administrativo',
        correo_institucional: '',
        rol: 'personal',
        password: '',
        confirmPassword: '',
        sexo: 'masculino',
        genero: 'masculino',
        edad: 25,
        estado_civil: 'soltero',
        religion: '',
        trabaja: true,
        lugar_trabajo: '',
        lugar_origen: '',
        colonia_residencia_actual: '',
        celular: '',
        discapacidad: '',
        observaciones: '',
        matricula: '',
        semestre: null,
        numero_hijos: 0,
        grupo_etnico: '',
        cohorte_id: null,
        programas_ids: [],
        grupos_ids: [],
      });
      setSelectedCohorte(null);
    }
  }, [open]);

  const handleChange = (field: keyof PersonaCreate) => (event: any) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Manejar selección de cohorte
  const handleCohorteChange = (event: any, newValue: Cohorte | null) => {
    setSelectedCohorte(newValue);
    setFormData(prev => ({
      ...prev,
      cohorte_id: newValue ? newValue.id : null
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validación básica
    if (!formData.correo_institucional || !formData.celular ||
        !formData.lugar_origen || !formData.password || !formData.matricula) {
      alert('Por favor, complete todos los campos requeridos.');
      return;
    }

    // Validación de matrícula (6 dígitos)
    if (!/^\d{6}$/.test(formData.matricula)) {
      alert('El número de empleado debe ser exactamente 6 dígitos.');
      return;
    }

    // Validación de contraseña
    if (formData.password.length < 6) {
      alert('La contraseña debe tener al menos 6 caracteres.');
      return;
    }

    // Validación de confirmación de contraseña
    if (formData.password !== formData.confirmPassword) {
      alert('Las contraseñas no coinciden.');
      return;
    }

    // Validación específica para personal
    if (!formData.lugar_trabajo) {
      alert('Por favor, especifique el área de trabajo.');
      return;
    }

    onSubmit(formData);
  };

  if (!open) return null;

  return (
    <Box component="form" onSubmit={handleSubmit}>
        <Grid container spacing={3}>
            
            <Grid size={{ xs: 12 }}>
              <Alert severity="info">
                <strong>Registro de Personal Académico:</strong> Su solicitud será revisada por el administrador antes de activar su cuenta.
              </Alert>
            </Grid>

            {/* Información Personal Básica */}
            <Grid size={{ xs: 12 }}>
              <Typography variant="h6" gutterBottom>
                Información Personal
              </Typography>
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                required
                label="Correo Institucional"
                type="email"
                value={formData.correo_institucional}
                onChange={handleChange('correo_institucional')}
                helperText="Use su correo institucional oficial"
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                required
                label="Contraseña"
                type="password"
                value={formData.password}
                onChange={handleChange('password')}
                helperText="Mínimo 6 caracteres"
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                required
                label="Confirmar Contraseña"
                type="password"
                value={formData.confirmPassword}
                onChange={handleChange('confirmPassword')}
                helperText="Repita la contraseña"
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                required
                label="Número de Empleado"
                value={formData.matricula}
                onChange={handleChange('matricula')}
                helperText="Exactamente 6 dígitos"
                inputProps={{ maxLength: 6, pattern: '[0-9]*' }}
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                required
                label="Teléfono Celular"
                value={formData.celular}
                onChange={handleChange('celular')}
              />
            </Grid>

            {/* Información Laboral */}
            <Grid size={{ xs: 12 }}>
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                Información Laboral
              </Typography>
            </Grid>

            <Grid size={{ xs: 12 }}>
              <FormControl fullWidth required>
                <InputLabel>Área de Trabajo</InputLabel>
                <Select
                  value={formData.lugar_trabajo}
                  label="Área de Trabajo"
                  onChange={handleChange('lugar_trabajo')}
                >
                  {areasPersonal.map((area) => (
                    <MenuItem key={area} value={area}>
                      {area}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Información Demográfica */}
            <Grid size={{ xs: 12 }}>
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                Información Demográfica
              </Typography>
            </Grid>

            <Grid size={{ xs: 12, sm: 4 }}>
              <FormControl fullWidth required>
                <InputLabel>Sexo</InputLabel>
                <Select
                  value={formData.sexo}
                  label="Sexo"
                  onChange={handleChange('sexo')}
                >
                  <MenuItem value="masculino">Masculino</MenuItem>
                  <MenuItem value="femenino">Femenino</MenuItem>
                  <MenuItem value="otro">Otro</MenuItem>
                  <MenuItem value="prefiero_no_especificar">Prefiero no especificar</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid size={{ xs: 12, sm: 4 }}>
              <FormControl fullWidth required>
                <InputLabel>Género</InputLabel>
                <Select
                  value={formData.genero}
                  label="Género"
                  onChange={handleChange('genero')}
                >
                  <MenuItem value="masculino">Masculino</MenuItem>
                  <MenuItem value="femenino">Femenino</MenuItem>
                  <MenuItem value="no_binario">No Binario</MenuItem>
                  <MenuItem value="otro">Otro</MenuItem>
                  <MenuItem value="prefiero_no_especificar">Prefiero no especificar</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid size={{ xs: 12, sm: 4 }}>
              <TextField
                fullWidth
                required
                label="Edad"
                type="number"
                value={formData.edad}
                onChange={handleChange('edad')}
                inputProps={{ min: 18, max: 70 }}
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <FormControl fullWidth required>
                <InputLabel>Estado Civil</InputLabel>
                <Select
                  value={formData.estado_civil}
                  label="Estado Civil"
                  onChange={handleChange('estado_civil')}
                >
                  <MenuItem value="soltero">Soltero/a</MenuItem>
                  <MenuItem value="casado">Casado/a</MenuItem>
                  <MenuItem value="divorciado">Divorciado/a</MenuItem>
                  <MenuItem value="viudo">Viudo/a</MenuItem>
                  <MenuItem value="union_libre">Unión Libre</MenuItem>
                  <MenuItem value="otro">Otro</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="Religión"
                value={formData.religion}
                onChange={handleChange('religion')}
              />
            </Grid>

            {/* Información Adicional */}
            <Grid size={{ xs: 12 }}>
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                Información Adicional
              </Typography>
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                required
                label="Lugar de Origen"
                value={formData.lugar_origen}
                onChange={handleChange('lugar_origen')}
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="Colonia de Residencia Actual"
                value={formData.colonia_residencia_actual}
                onChange={handleChange('colonia_residencia_actual')}
                helperText="Opcional"
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="Número de Hijos"
                type="number"
                value={formData.numero_hijos}
                onChange={handleChange('numero_hijos')}
                inputProps={{ min: 0, max: 20 }}
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="Grupo Étnico"
                value={formData.grupo_etnico}
                onChange={handleChange('grupo_etnico')}
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="Discapacidad"
                value={formData.discapacidad}
                onChange={handleChange('discapacidad')}
                helperText="Especifique si tiene alguna discapacidad"
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <Autocomplete
                options={cohortes}
                getOptionLabel={(option) => `${option.nombre} (${option.anio})`}
                value={selectedCohorte}
                onChange={handleCohorteChange}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Cohorte"
                    helperText="Seleccione la cohorte si aplica"
                  />
                )}
              />
            </Grid>

            <Grid size={{ xs: 12 }}>
              <TextField
                fullWidth
                label="Observaciones"
                multiline
                rows={3}
                value={formData.observaciones}
                onChange={handleChange('observaciones')}
                helperText="Información adicional relevante sobre su función o responsabilidades"
              />
            </Grid>

            {/* Botones */}
            <Grid size={{ xs: 12 }}>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 3 }}>
                <Button
                  variant="outlined"
                  onClick={onClose}
                  disabled={loading}
                >
                  Cancelar
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  disabled={loading}
                  sx={{ bgcolor: 'success.main' }}
                >
                  {loading ? 'Registrando...' : 'Registrar Personal'}
                </Button>
              </Box>
            </Grid>
        </Grid>
    </Box>
  );
};

export default PersonalRegistroForm;
