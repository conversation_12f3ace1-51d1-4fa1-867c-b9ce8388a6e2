import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  AppBar,
  Toolbar,
  IconButton
} from '@mui/material';
import {
  School as SchoolIcon,
  Person as PersonIcon,
  Work as WorkIcon,
  ArrowBack as ArrowBackIcon
} from '@mui/icons-material';

const RegistroSeleccionPage = () => {
  const navigate = useNavigate();

  const tiposUsuario = [
    {
      tipo: 'estudiante',
      titulo: 'Estudiante',
      descripcion: 'Registro para estudiantes de la institución',
      icono: <SchoolIcon sx={{ fontSize: 60, color: 'primary.main' }} />,
      ruta: '/registro-alumno',
      color: 'primary.main'
    },
    {
      tipo: 'docente',
      titulo: 'Docente',
      descripcion: 'Registro para profesores y personal docente',
      icono: <PersonIcon sx={{ fontSize: 60, color: 'secondary.main' }} />,
      ruta: '/registro-docente',
      color: 'secondary.main'
    },
    {
      tipo: 'personal',
      titulo: 'Personal Académico',
      descripcion: 'Registro para personal administrativo y académico',
      icono: <WorkIcon sx={{ fontSize: 60, color: 'success.main' }} />,
      ruta: '/registro-personal',
      color: 'success.main'
    }
  ];

  return (
    <Box sx={{ flexGrow: 1, minHeight: '100vh', bgcolor: 'grey.50' }}>
      {/* App Bar */}
      <AppBar position="static" sx={{ bgcolor: 'primary.main' }}>
        <Toolbar>
          <Button
            color="inherit"
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/login')}
            sx={{ mr: 2 }}
          >
            Volver al Login
          </Button>
          
          <SchoolIcon sx={{ mr: 2 }} />
          
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            Sistema SSP - Registro de Usuario
          </Typography>
        </Toolbar>
      </AppBar>

      {/* Contenido Principal */}
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Paper elevation={3} sx={{ p: 4 }}>
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <Typography variant="h4" component="h1" gutterBottom>
              Selecciona tu Tipo de Usuario
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Elige la opción que mejor describa tu rol en la institución
            </Typography>
          </Box>

          <Grid container spacing={4} justifyContent="center">
            {tiposUsuario.map((tipo) => (
              <Grid item xs={12} sm={6} md={4} key={tipo.tipo}>
                <Card 
                  sx={{ 
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    transition: 'transform 0.2s, box-shadow 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: 4
                    }
                  }}
                >
                  <CardContent sx={{ flexGrow: 1, textAlign: 'center', p: 3 }}>
                    <Box sx={{ mb: 2 }}>
                      {tipo.icono}
                    </Box>
                    <Typography variant="h5" component="h2" gutterBottom>
                      {tipo.titulo}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {tipo.descripcion}
                    </Typography>
                  </CardContent>
                  <CardActions sx={{ justifyContent: 'center', p: 2 }}>
                    <Button
                      variant="contained"
                      size="large"
                      onClick={() => navigate(tipo.ruta)}
                      sx={{ 
                        bgcolor: tipo.color,
                        '&:hover': {
                          bgcolor: tipo.color,
                          filter: 'brightness(0.9)'
                        }
                      }}
                    >
                      Registrarse
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>

          <Box sx={{ mt: 4, textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              ¿Ya tienes una cuenta?{' '}
              <Button 
                variant="text" 
                onClick={() => navigate('/login')}
                sx={{ textTransform: 'none' }}
              >
                Inicia sesión aquí
              </Button>
            </Typography>
          </Box>
        </Paper>
      </Container>
    </Box>
  );
};

export default RegistroSeleccionPage;
