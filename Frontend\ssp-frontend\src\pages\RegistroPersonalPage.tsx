import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Snackbar,
  Alert,
  AppBar,
  Toolbar,
  IconButton
} from '@mui/material';
import {
  Work as WorkIcon,
  ArrowBack as ArrowBackIcon
} from '@mui/icons-material';
import PersonalRegistroForm from '../components/PersonalRegistroForm';
import type { PersonaCreate } from '../types/index';
import { personasApi } from '@/services/api';

const RegistroPersonalPage = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error'
  });

  const showSnackbar = (message: string, severity: 'success' | 'error') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  // Manejar registro de personal
  const handleRegistroPersonal = async (personalData: PersonaCreate) => {
    try {
      setLoading(true);
      await personasApi.registroPersonal(personalData);
      
      showSnackbar(
        '¡Registro exitoso! Su solicitud será revisada por el administrador. Recibirá un correo cuando sea aprobada.',
        'success'
      );
      
      // Redirigir al login después de 3 segundos
      setTimeout(() => {
        navigate('/login');
      }, 3000);
      
    } catch (error: any) {
      console.error('Error en registro:', error);
      const errorMessage = error.response?.data?.detail || 'Error al registrar el personal';
      showSnackbar(errorMessage, 'error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ flexGrow: 1, minHeight: '100vh', bgcolor: 'grey.50' }}>
      {/* App Bar */}
      <AppBar position="static" sx={{ bgcolor: 'success.main' }}>
        <Toolbar>
          <Button
            color="inherit"
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/registro')}
            sx={{ mr: 2 }}
          >
            Volver a Selección
          </Button>
          
          <WorkIcon sx={{ mr: 2 }} />
          
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            Sistema SSP - Registro de Personal Académico
          </Typography>
        </Toolbar>
      </AppBar>

      {/* Contenido Principal */}
      <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
        <Paper elevation={3} sx={{ p: 4 }}>
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <WorkIcon sx={{ fontSize: 60, color: 'success.main', mb: 2 }} />
            <Typography variant="h4" component="h1" gutterBottom>
              Registro de Personal Académico
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Complete el formulario para solicitar su cuenta de personal académico en el Sistema de Seguimiento Psicopedagógico
            </Typography>
          </Box>

          <PersonalRegistroForm
            open={true}
            onClose={() => navigate('/registro')}
            onSubmit={handleRegistroPersonal}
            loading={loading}
          />
        </Paper>
      </Container>

      {/* Snackbar para mensajes */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default RegistroPersonalPage;
